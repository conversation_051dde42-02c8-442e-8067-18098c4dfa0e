"use client";

import { NumericFormat } from "react-number-format";
import { ChevronDownIcon } from "@/assets/icons";
import { removeCommas } from "@/utils/helper";
import { minusBN, plusBN } from "@/utils/number";
import { removeTrailingZeros } from "@/utils/format";
import BigNumber from "bignumber.js";
import { useSelector } from "react-redux";
import { RootState } from "../../../store/index";
import { AppPopover } from "@/components";
import { useState } from "react";
import { TYPE_LAYOUT } from "@/constants/common";

export enum EMarketInputMode {
  BASE = "base",
  QUOTE = "quote",
}

export type TInputMode = EMarketInputMode;

interface IInputAmountWithSelectorProps {
  value: string;
  onChange: (value: string) => void;
  label: string;
  placeholder?: string;
  decimal?: number;
  isHasArrowChange?: boolean;
  inputMode: TInputMode;
  onInputModeChange: (mode: TInputMode) => void;
  baseAsset: string;
  quoteAsset: string;
  minimumText?: string;
}

export const InputAmountWithSelector = ({
  value,
  onChange,
  label,
  placeholder,
  decimal = 6,
  isHasArrowChange = false,
  inputMode,
  onInputModeChange,
  baseAsset,
  quoteAsset,
  minimumText,
}: IInputAmountWithSelectorProps) => {
  const [isShowSelector, setIsShowSelector] = useState<boolean>(false);
  const { type } = useSelector(
    (state: RootState) => state.metadata.settingsLayout
  );
  const isLayoutAdvanced = type === TYPE_LAYOUT.ADVANCED;

  const currentAsset =
    inputMode === EMarketInputMode.BASE ? baseAsset : quoteAsset;
  const currentLabel = inputMode === EMarketInputMode.BASE ? "Amount" : "Total";

  const selectorOptions = [
    {
      label: baseAsset.toUpperCase(),
      value: EMarketInputMode.BASE,
      description: "Amount",
    },
    {
      label: quoteAsset.toUpperCase(),
      value: EMarketInputMode.QUOTE,
      description: "Total",
    },
  ];

  return (
    <div className="w-full">
      {isLayoutAdvanced && (
        <div className="body-sm-medium-12 text-white-700 mb-2">
          {currentLabel}
        </div>
      )}

      <div className="border-white-100 hover:border-white-1000 flex w-full rounded-[6px] border">
        <div
          className={`flex px-3 py-2 ${
            isLayoutAdvanced
              ? "w-[calc(100%-20px)] flex-1 justify-between"
              : "w-full"
          }`}
        >
          {!isLayoutAdvanced && (
            <div className="body-md-regular-14 text-white-300">
              {currentLabel}
            </div>
          )}

          <NumericFormat
            value={value}
            placeholder={
              placeholder ||
              (minimumText ? `Minimum ${minimumText}` : undefined)
            }
            onChange={(values) => {
              onChange(removeCommas(values.target.value));
            }}
            thousandSeparator=","
            decimalSeparator="."
            allowNegative={false}
            className={`body-md-medium-14 !font-rotobo-mono placeholder:font-mona-sans placeholder:text-white-500 ml-auto flex-1 bg-transparent focus:outline-none ${
              isLayoutAdvanced ? "min-w-[20px] text-left" : "text-right"
            }`}
            allowLeadingZeros={false}
            decimalScale={decimal}
            inputMode="numeric"
          />

          {/* Selectable prefix */}
          <AppPopover
            trigger={
              <div className="body-md-medium-14 ml-1 flex cursor-pointer items-center gap-1">
                {currentAsset.toUpperCase()}
                <ChevronDownIcon
                  className={`${isShowSelector ? "rotate-180" : ""}`}
                />
              </div>
            }
            content={
              <div
                style={{
                  boxShadow:
                    "4px 4px 8px 0px var(--Black-500, rgba(8, 9, 12, 0.50))",
                  background:
                    "linear-gradient(0deg, var(--White-100, rgba(255, 255, 255, 0.10)) 0%, var(--White-100, rgba(255, 255, 255, 0.10)) 100%), var(--Black-900, #08090C)",
                }}
                className="flex min-w-[80px] flex-col gap-2 rounded-[6px] px-3 py-2"
              >
                {selectorOptions.map((option) => (
                  <div
                    key={option.value}
                    className={`body-sm-medium-12 hover:text-white-1000 cursor-pointer text-left ${
                      inputMode === option.value
                        ? "text-white-1000"
                        : "text-white-500"
                    }`}
                    onClick={() => {
                      onInputModeChange(option.value);
                      setIsShowSelector(false);
                    }}
                  >
                    <div>{option.label}</div>
                  </div>
                ))}
              </div>
            }
            isOpen={isShowSelector}
            onToggle={() => setIsShowSelector(!isShowSelector)}
            onClose={() => setIsShowSelector(false)}
            position="right"
          />
        </div>

        {isHasArrowChange && (
          <div className="border-white-100 w-[24px] border-l">
            <div
              className="border-white-100  text-white-500 hover:text-white-1000 flex cursor-pointer items-center justify-center border-b py-[6.5px]"
              onClick={() =>
                onChange(
                  plusBN(
                    value,
                    BigNumber(1).dividedBy(BigNumber(10).pow(decimal))
                  )
                )
              }
            >
              <ChevronDownIcon className="rotate-[180deg]" />
            </div>
            <div
              className="text-white-500 hover:text-white-1000 flex cursor-pointer items-center justify-center py-[6px]"
              onClick={() => {
                const decrementValue = BigNumber(1).dividedBy(
                  BigNumber(10).pow(decimal)
                );
                const newValue = minusBN(value, decrementValue);

                // Only trigger onChange if the result is not negative
                if (BigNumber(newValue).isGreaterThanOrEqualTo(0)) {
                  onChange(newValue);
                }
              }}
            >
              <ChevronDownIcon />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
