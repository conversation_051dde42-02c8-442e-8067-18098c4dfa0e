"use client";

import { NumericFormat } from "react-number-format";
import { ChevronDownIcon } from "@/assets/icons";
import { removeCommas } from "@/utils/helper";
import { minusBN, plusBN } from "@/utils/number";
import BigNumber from "bignumber.js";
import { useSelector } from "react-redux";
import { RootState } from "@/store/index";
import { TYPE_LAYOUT } from "@/constants/common";

export const InputAmount = ({
  value,
  onChange,
  label,
  prefix,
  placeholder,
  decimal = 6,
  isHasArrowChange = false,
}: {
  value: string;
  prefix?: string;
  placeholder?: string;
  label: string;
  decimal?: number;
  onChange: (value: string) => void;
  isHasArrowChange?: boolean;
}) => {
  const { type } = useSelector(
    (state: RootState) => state.metadata.settingsLayout
  );
  const isLayoutAdvanced = type === TYPE_LAYOUT.ADVANCED;

  return (
    <div className="w-full">
      {isLayoutAdvanced && (
        <div className="body-sm-medium-12 text-white-700 mb-2">{label}</div>
      )}

      <div className="border-white-100 hover:border-white-1000 flex w-full rounded-[6px] border">
        <div
          className={`flex px-3 py-2 ${
            isLayoutAdvanced
              ? "w-[calc(100%-24px)] flex-1 justify-between"
              : "w-[calc(100%-24px)] flex-1"
          }`}
        >
          {!isLayoutAdvanced && (
            <div className="body-md-regular-14 text-white-300 mr-1">
              {label}
            </div>
          )}

          <NumericFormat
            value={value}
            placeholder={placeholder}
            onChange={(values) => {
              onChange(removeCommas(values.target.value));
            }}
            thousandSeparator=","
            decimalSeparator="."
            allowNegative={false}
            className={`body-md-medium-14 !font-rotobo-mono placeholder:font-mona-sans placeholder:text-white-500 ml-auto min-w-[40px] flex-1 truncate bg-transparent focus:outline-none ${
              isLayoutAdvanced ? "min-w-[20px] text-left" : "text-right"
            }`}
            allowLeadingZeros={false}
            decimalScale={decimal}
            inputMode="numeric"
          />
          {prefix && <div className="body-md-medium-14 ml-1">{prefix}</div>}
        </div>

        {isHasArrowChange && (
          <div className="border-white-100 w-[24px] border-l">
            <div
              className="border-white-100  text-white-500 hover:text-white-1000 flex cursor-pointer items-center justify-center border-b py-[6.5px]"
              onClick={() =>
                onChange(
                  plusBN(
                    value,
                    BigNumber(1).dividedBy(BigNumber(10).pow(decimal))
                  )
                )
              }
            >
              <ChevronDownIcon className="rotate-[180deg]" />
            </div>
            <div
              className="text-white-500 hover:text-white-1000 flex cursor-pointer items-center justify-center py-[6px]"
              onClick={() => {
                const decrementValue = BigNumber(1).dividedBy(
                  BigNumber(10).pow(decimal)
                );
                const newValue = minusBN(value, decrementValue);

                // Only trigger onChange if the result is not negative
                if (BigNumber(newValue).isGreaterThanOrEqualTo(0)) {
                  onChange(newValue);
                }
              }}
            >
              <ChevronDownIcon />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
