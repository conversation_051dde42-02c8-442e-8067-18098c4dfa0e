"use client";

import React, { memo, useEffect, useState, useMemo } from "react";
import { AppButtonSort } from "@/components";
import { useMediaQuery } from "react-responsive";
import { TBalanceAsset } from "@/types/account";
import AppNumber from "@/components/AppNumber";
import _ from "lodash";
import { useMultipleTickers } from "@/hooks/useTicker";
import { Ticker } from "@/types/pair";
import { useSelector } from "react-redux";
import { RootState } from "@/store/index";
import { NotFoundIcon } from "@/assets/icons";

const CoinItem = ({
  coin,
  tickers,
}: {
  coin: TBalanceAsset & { price: string };
  tickers: { [symbol: string]: Ticker };
}) => {
  const isMobile = useMediaQuery({ query: "(max-width: 992px)" });
  const assets = useSelector((state: RootState) => state.metadata.assets);

  const assetData = useMemo(
    () => assets.find((item) => item.symbol === coin?.asset),
    [assets, coin]
  );

  // Get price from tickers
  const coinPrice = useMemo(() => {
    if (coin.asset === "USDT") return "1";
    const tickerSymbol = `${coin.asset?.toUpperCase()}USDT`;
    const ticker = tickers[tickerSymbol];
    return ticker?.lastPrice || "0";
  }, [coin.asset, tickers]);

  if (isMobile) {
    return (
      <div className="border-white-50 flex gap-2 border-b py-2">
        <div className="flex flex-1 flex-col gap-2">
          <div className="flex justify-between">
            <div className="body-md-medium-14 flex items-center gap-2">
              <img
                src={assetData?.logoUrl}
                alt={coin.asset}
                width={20}
                height={20}
                className="aspect-square h-[20px] w-[20px] rounded-full"
              />
              {coin.asset}
            </div>
            <div className="body-md-medium-14">
              <AppNumber value={coin.total} decimals={10} />
            </div>
          </div>
          <div className="ml-7 flex justify-between">
            <div className="body-md-regular-14 text-white-500">Price</div>
            <div className="body-md-regular-14 text-white-500">
              <AppNumber
                value={coinPrice}
                isForUSD
                isFormatLargeNumber={false}
              />
            </div>
          </div>

          <div className="ml-7 flex justify-between">
            <div className="body-md-regular-14 text-white-500">USD Value</div>
            <div className="body-md-regular-14 text-white-500">
              <AppNumber
                isForUSD
                value={coin.totalBalanceInUsd || "0"}
                isFormatLargeNumber={false}
              />
            </div>
          </div>
          {/* <div className="flex justify-between">
            <div className="body-md-regular-14 text-white-500">
              Today&apos;s PNL
            </div>
            <div className="body-md-regular-14 text-red-400">--</div>
          </div> */}
          {/* <div className="flex justify-between">
            <div className="body-md-regular-14 text-white-500">Cost Price</div>
            <div className="body-md-regular-14 text-white-500">--</div>
          </div> */}
        </div>
      </div>
    );
  }

  return (
    <div className="border-white-50 hover:bg-white-50 flex w-full cursor-pointer items-center lg:border-b">
      <div className="w-[25%] px-2 py-2.5 text-left lg:min-w-[184px]">
        <div className="flex items-center gap-2">
          <img
            src={assetData?.logoUrl}
            alt={coin.asset}
            width={24}
            height={24}
            className="aspect-square h-[24px] w-[24px] rounded-full"
          />
          <div>
            <div className="body-sm-regular-12">{coin.asset}</div>
            <div className="body-xs-regular-10 text-white-500">
              {assetData?.name}
            </div>
          </div>
        </div>
      </div>
      <div className="body-sm-regular-12 w-[25%] px-2 py-2.5 lg:min-w-[184px]">
        <div className="flex w-full flex-col items-end">
          <div className="body-sm-regular-12">
            <AppNumber
              value={coin.total}
              isFormatLargeNumber={false}
              decimals={10}
            />
          </div>
        </div>
      </div>
      <div className="body-sm-regular-12 w-[25%] px-2 py-2.5 lg:min-w-[184px]">
        <div className="flex w-full flex-col items-end">
          <div className="body-sm-regular-12">
            <AppNumber value={coinPrice} isFormatLargeNumber={false} isForUSD />
          </div>
          {/* <div className="body-xs-regular-10 text-white-500">--</div> */}
        </div>
      </div>
      <div className="body-sm-regular-12 w-[25%] px-2 py-2.5 lg:min-w-[184px]">
        <div className="flex w-full flex-col items-end">
          <div className="body-sm-regular-12">
            <AppNumber
              isForUSD
              value={coin.totalBalanceInUsd || "0"}
              isFormatLargeNumber={false}
            />
          </div>
        </div>
      </div>
      {/* <div className="body-sm-regular-12 flex w-[20%] items-center justify-end gap-4 px-2 py-2.5 lg:w-[20%] lg:min-w-[184px]">
        <div className="text-right text-right text-green-500">--</div>
        <ChevronDownIcon />
      </div> */}
    </div>
  );
};

CoinItem.displayName = "CoinItem";

export const TableCoinView = memo(
  ({ balances }: { balances: TBalanceAsset[] }) => {
    const [sortBy, setSortBy] = useState<string>("");
    const [sortType, setSortType] = useState<string>("");
    const [isMounted, setIsMounted] = useState<boolean>(false);
    const [balancesShow, setBalancesShow] = useState<
      (TBalanceAsset & { price: string })[]
    >([]);
    const { tickers } = useMultipleTickers();

    useEffect(() => {
      setIsMounted(true);
    }, []);

    useEffect(() => {
      let dataBalance: (TBalanceAsset & { price: string })[] = balances.map(
        (balance) => {
          // Add price information for sorting
          // USDT is special case, always 1 USD. Other coins get their price from tickers
          const coinPrice =
            balance.asset === "USDT"
              ? "1"
              : tickers[`${balance.asset?.toUpperCase()}USDT`]?.lastPrice ||
                "0";
          return {
            ...balance,
            price: coinPrice,
          };
        }
      );

      if (sortType === "desc") {
        dataBalance = _.orderBy(
          dataBalance,
          [
            (coin: TBalanceAsset & { price: string }) => {
              if (
                sortBy === "available" ||
                sortBy === "totalBalanceInUsd" ||
                sortBy === "total" ||
                sortBy === "price"
              ) {
                return Number(coin[sortBy as keyof typeof coin] || 0);
              }
              return coin[sortBy as keyof typeof coin];
            },
          ],
          ["desc"]
        );
      }

      if (sortType === "asc") {
        dataBalance = _.orderBy(
          dataBalance,
          [
            (coin: TBalanceAsset & { price: string }) => {
              if (
                sortBy === "available" ||
                sortBy === "totalBalanceInUsd" ||
                sortBy === "total" ||
                sortBy === "price"
              ) {
                return Number(coin[sortBy as keyof typeof coin] || 0);
              }
              return coin[sortBy as keyof typeof coin];
            },
          ],
          ["asc"]
        );
      }

      setBalancesShow(dataBalance);
    }, [balances, sortBy, sortType, tickers]);

    if (!isMounted) return <></>;

    return (
      <>
        <div className="w-full">
          <div className="hidden w-full items-center md:flex">
            <div className="body-sm-regular-12 text-white-500 flex w-[25%] items-center px-2 py-1.5 md:min-w-[184px] ">
              <div className="flex items-center gap-2">
                Coin
                <AppButtonSort
                  value="asset"
                  sortBy={sortBy}
                  sortType={sortType}
                  setSortType={setSortType}
                  setSortBy={setSortBy}
                />
              </div>
            </div>
            <div className="body-sm-regular-12 text-white-500 w-[25%] px-2 py-1.5 text-left md:min-w-[184px]">
              <div className="flex items-center justify-end gap-2">
                Amount
                <AppButtonSort
                  value="total"
                  sortBy={sortBy}
                  sortType={sortType}
                  setSortType={setSortType}
                  setSortBy={setSortBy}
                />
              </div>
            </div>
            <div className="body-sm-regular-12 text-white-500 w-[25%] px-2 py-1.5 text-left md:min-w-[184px]">
              <div className="flex items-center justify-end gap-2">
                Price
                <AppButtonSort
                  value="price"
                  sortBy={sortBy}
                  sortType={sortType}
                  setSortType={setSortType}
                  setSortBy={setSortBy}
                />
              </div>
            </div>
            <div className="body-sm-regular-12 text-white-500 w-[25%] px-2 py-1.5 text-left md:min-w-[184px]">
              <div className="flex items-center justify-end gap-2">
                USD Value
                <AppButtonSort
                  value="totalBalanceInUsd"
                  sortBy={sortBy}
                  sortType={sortType}
                  setSortType={setSortType}
                  setSortBy={setSortBy}
                />
              </div>
            </div>
            {/* <div className="body-sm-regular-12 text-white-500 w-[20%] px-2 py-1.5 text-left md:min-w-[184px]">
            <div className="flex items-center justify-end gap-2">
              Today&apos;s PnL
              <AppButtonSort
                value="pnl"
                sortBy={sortBy}
                sortType={sortType}
                setSortType={setSortType}
                setSortBy={setSortBy}
              />
            </div>
          </div> */}
          </div>

          <div className="customer-scroll h-[400px] w-full overflow-y-auto">
            {!!balancesShow?.length ? (
              balancesShow.map(
                (coin: TBalanceAsset & { price: string }, index: number) => {
                  return <CoinItem key={index} coin={coin} tickers={tickers} />;
                }
              )
            ) : (
              <div className="mt-8 flex h-full flex-1 flex-col items-center justify-center">
                <NotFoundIcon />
                <div className="text-white-500 body-md-regular-14">
                  No Assets Found
                </div>
              </div>
            )}
          </div>
        </div>
      </>
    );
  }
);

TableCoinView.displayName = "TableCoinView";
