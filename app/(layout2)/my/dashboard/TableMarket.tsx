"use client";

import React, { memo, useEffect, useState, useMemo } from "react";
import { AppButtonSort } from "@/components";
import { useMediaQuery } from "react-responsive";
import { useSelector } from "react-redux";
import { RootState } from "@/store/index";
import { TAsset } from "@/types/asset";
import _ from "lodash";
import useAccountBalance from "@/hooks/useAccountBalance";
import { TBalance } from "@/types/account";
import CoinItem from "./components/CoinItem";

const SORT_CONFIGS = {
  symbol: {
    field: "symbol",
    type: "string" as const,
    transform: (value: any) => value?.toLowerCase() || "",
  },
  totalBalanceInUsd: {
    field: "totalBalanceInUsd",
    type: "number" as const,
    transform: (value: any) => Number(value || 0),
  },
  price: {
    field: "price",
    type: "number" as const,
    transform: (value: any) => Number(value || 0),
  },
  change24h: {
    field: "change24h",
    type: "number" as const,
    transform: (value: any) => Number(value || 0),
  },
} as const;

export const TableMarkets = memo(() => {
  const [sortBy, setSortBy] = useState<string>("totalBalanceInUsd");
  const [sortType, setSortType] = useState<string>("desc");
  const [isMounted, setIsMounted] = useState<boolean>(false);
  const { accountBalances } = useAccountBalance({});
  const tickers = useSelector((state: RootState) => state.ticker.tickers);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  const sortedAssets = useMemo(() => {
    if (!sortBy || !sortType) return accountBalances;

    const enhancedAssets = accountBalances.map((assetBalance: TBalance) => {
      const coinPrice = tickers.find(
        (t) =>
          t.symbol?.toUpperCase() === `${assetBalance.asset?.toUpperCase()}USDT`
      );

      return {
        assetBalance,
        sortValues: {
          symbol: assetBalance.asset?.toLowerCase() || "",
          totalBalanceInUsd: Number(assetBalance?.totalBalanceInUsd || 0),
          price: Number(
            assetBalance.asset === "USDT" ? "1" : coinPrice?.lastPrice || 0
          ),
          change24h: Number(coinPrice?.priceChangePercent || 0),
        },
      };
    });

    const config = SORT_CONFIGS[sortBy as keyof typeof SORT_CONFIGS];

    if (!config) return accountBalances;

    const sorted = _.orderBy(
      enhancedAssets,
      [
        (item) =>
          config.transform(
            item.sortValues[config.field as keyof typeof item.sortValues]
          ),
      ],
      [sortType as "asc" | "desc"]
    );

    return sorted.map((item) => item.assetBalance);
  }, [accountBalances, tickers, sortBy, sortType]);

  if (!isMounted) return <></>;

  return (
    <>
      <div className="w-full">
        <div className="flex w-full items-center md:hidden">
          <div className="body-sm-regular-12 text-white-500 flex w-[40%] items-center px-2 py-1.5 ">
            Name
          </div>
          <div className="body-sm-regular-12 text-white-500 w-[30%] px-2 py-1.5 text-left ">
            <div className="flex items-center justify-end">Last price</div>
          </div>
          <div className="body-sm-regular-12 text-white-500 w-[30%] px-2 py-1.5 text-left">
            <div className="flex items-center justify-end gap-2">
              Change (%)
            </div>
          </div>
        </div>

        <div className="hidden w-full items-center md:flex">
          <div className="body-sm-regular-12 text-white-500 flex w-[20%] items-center px-2 py-1.5 md:min-w-[184px] ">
            <div className="flex items-center gap-2">
              Coin
              <AppButtonSort
                value="symbol"
                sortBy={sortBy}
                sortType={sortType}
                setSortType={setSortType}
                setSortBy={setSortBy}
              />
            </div>
          </div>
          <div className="body-sm-regular-12 text-white-500 w-[20%] px-2 py-1.5 text-left md:min-w-[184px]">
            <div className="flex items-center justify-end gap-2">
              Amount / USD Value
              <AppButtonSort
                value="totalBalanceInUsd"
                sortBy={sortBy}
                sortType={sortType}
                setSortType={setSortType}
                setSortBy={setSortBy}
              />
            </div>
          </div>
          <div className="body-sm-regular-12 text-white-500 w-[20%] px-2 py-1.5 text-left md:min-w-[184px]">
            <div className="flex items-center justify-end gap-2">
              Price
              <AppButtonSort
                value="price"
                sortBy={sortBy}
                sortType={sortType}
                setSortType={setSortType}
                setSortBy={setSortBy}
              />
            </div>
          </div>
          <div className="body-sm-regular-12 text-white-500 w-[20%] px-2 py-1.5 text-left md:min-w-[184px]">
            <div className="flex items-center justify-end gap-2">
              24h Change
              <AppButtonSort
                value="change24h"
                sortBy={sortBy}
                sortType={sortType}
                setSortType={setSortType}
                setSortBy={setSortBy}
              />
            </div>
          </div>
          <div className="body-sm-regular-12 text-white-500 w-[20%] px-2 py-1.5 text-right md:min-w-[184px]">
            Trade
          </div>
        </div>

        <div>
          {sortedAssets.map((asset: TBalance, index: number) => {
            return <CoinItem key={index} asset={asset} />;
          })}
        </div>
      </div>
    </>
  );
});

TableMarkets.displayName = "TableMarkets";
