"use client";

import React, { memo, useMemo } from "react";
import { AppNumber } from "@/components/AppNumber";
import { useMediaQuery } from "react-responsive";
import Link from "next/link";
import { useSelector } from "react-redux";
import { RootState } from "@/store/index";
import { TBalance } from "@/types/account";
import { getPriceStyle } from "@/utils/helper";
import BigNumber from "bignumber.js";
import { DEFAULT_DECIMAL, DEFAULT_TRADING_PAIR } from "@/constants";
import { AppButton } from "@/components";
import { isZero } from "@/utils/number";

const CoinItem = memo(
  ({ asset }: { asset: TBalance }) => {
    const isMobile = useMediaQuery({ query: "(max-width: 992px)" });
    const tickers = useSelector((state: RootState) => state.ticker.tickers);
    const assets = useSelector((state: RootState) => state.metadata.assets);

    const coinPriceInUsd = useMemo(() => {
      return tickers.find(
        (t) => t.symbol?.toUpperCase() === `${asset.asset?.toUpperCase()}USDT`
      );
    }, [tickers, asset.asset]);

    const assetData = useMemo(
      () => assets.find((item) => item.symbol === asset?.asset),
      [assets, asset]
    );

    const displayValues = useMemo(() => {
      return {
        balance: asset.total,
        totalBalanceInUsd: asset?.totalBalanceInUsd || "0",
        price:
          asset.asset === "USDT"
            ? "1"
            : coinPriceInUsd?.lastPrice?.toString() || "0",
        change24h: coinPriceInUsd?.priceChangePercent || "0",
        tradingPair:
          asset.asset === "USDT"
            ? DEFAULT_TRADING_PAIR
            : `${asset.asset?.toLowerCase()}usdt`,
      };
    }, [asset, coinPriceInUsd]);

    const getStyleButton = (priceChange: string | null) => {
      if (!priceChange || isZero(priceChange)) return "secondary";

      if (BigNumber(priceChange).isGreaterThan(0)) {
        return "form-buy";
      }

      return "sell";
    };

    return (
      <div className="border-white-50 hover:bg-white-50 flex w-full items-center lg:border-b">
        <div className="flex w-[40%] items-center gap-2 px-2 py-2.5 text-left lg:w-[20%] lg:min-w-[184px]">
          <img
            src={assetData?.logoUrl}
            alt={asset.asset}
            width={24}
            height={24}
            className="aspect-square h-[24px] w-[24px] rounded-full"
          />
          <div className="flex flex-col justify-end">
            <div className="body-sm-regular-12">{asset.asset}</div>
            <div className="body-xs-regular-10 text-white-500">
              {assetData?.name}
            </div>
          </div>
        </div>

        <div className="body-sm-regular-12 hidden w-[30%] px-2 py-2.5 lg:block lg:w-[20%] lg:min-w-[184px]">
          <div className="flex w-full flex-col items-end">
            <div className="body-sm-regular-12">
              <AppNumber
                value={displayValues.balance}
                decimals={DEFAULT_DECIMAL}
                isFormatLargeNumber={false}
              />
            </div>
            <div className="body-xs-regular-10 text-white-500">
              <AppNumber
                isForUSD
                value={displayValues.totalBalanceInUsd}
                isFormatLargeNumber={false}
              />
            </div>
          </div>
        </div>

        <div className="body-sm-regular-12 w-[30%] px-2 py-2.5 lg:w-[20%] lg:min-w-[184px]">
          <div className="flex w-full flex-col items-end">
            <div className="body-sm-regular-12">
              <AppNumber
                value={displayValues.price}
                isFormatLargeNumber={false}
                isForUSD
              />
            </div>
            {/* <div className="body-xs-regular-10 text-white-500">--</div> */}
          </div>
        </div>

        <div className="body-sm-regular-12 w-[30%] px-2 py-2.5 lg:w-[20%] lg:min-w-[184px]">
          {isMobile ? (
            <div className="flex justify-end">
              <Link
                href={`/trade/${displayValues.tradingPair?.toLocaleLowerCase()}`}
              >
                <AppButton
                  size="small"
                  className="w-[80px] text-[10px]"
                  variant={getStyleButton(displayValues.change24h || "0")}
                >
                  <AppNumber
                    value={displayValues.change24h || "0"}
                    decimals={2}
                    isFormatLargeNumber={false}
                    isForPercent
                  />
                </AppButton>
              </Link>
            </div>
          ) : (
            <div
              className="text-right"
              style={{
                color: getPriceStyle(displayValues.change24h || "0"),
              }}
            >
              <div className="flex justify-end">
                <AppNumber
                  value={displayValues?.change24h || 0}
                  isFormatLargeNumber={false}
                  isForPercent
                />
              </div>
            </div>
          )}
        </div>
        <div className="hidden w-[20%] justify-end px-2 py-2.5 md:min-w-[184px] lg:flex">
          <Link
            href={`/trade/${displayValues.tradingPair?.toLocaleLowerCase()}`}
            className="body-sm-medium-12 underline"
          >
            Trade
          </Link>
        </div>
      </div>
    );
  }
);

CoinItem.displayName = "CoinItem";

export default CoinItem;
